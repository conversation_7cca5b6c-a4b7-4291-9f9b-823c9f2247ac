"""
Attention mechanisms for ThinkerLLM components.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Optional, Tuple
import math

try:
    from flash_attn import flash_attn_func
    FLASH_ATTENTION_AVAILABLE = True
except ImportError:
    FLASH_ATTENTION_AVAILABLE = False


class ThinkerAttention(nn.Module):
    """
    Non-autoregressive attention mechanism for ThinkerModule.
    Processes entire sequences simultaneously without causal masking.
    """

    def __init__(
        self,
        hidden_size: int,
        num_heads: int,
        dropout: float = 0.1,
        use_flash_attention: bool = True,
    ):
        super().__init__()
        self.hidden_size = hidden_size
        self.num_heads = num_heads
        self.head_dim = hidden_size // num_heads
        self.dropout = dropout
        self.use_flash_attention = use_flash_attention and FLASH_ATTENTION_AVAILABLE

        assert hidden_size % num_heads == 0, "hidden_size must be divisible by num_heads"

        self.q_proj = nn.Linear(hidden_size, hidden_size, bias=False)
        self.k_proj = nn.Linear(hidden_size, hidden_size, bias=False)
        self.v_proj = nn.Linear(hidden_size, hidden_size, bias=False)
        self.o_proj = nn.Linear(hidden_size, hidden_size, bias=False)

        self.dropout_layer = nn.Dropout(dropout)

    def forward(
        self,
        hidden_states: torch.Tensor,
        attention_mask: Optional[torch.Tensor] = None,
        position_embeddings: Optional[torch.Tensor] = None,
    ) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Forward pass for non-autoregressive attention.

        Args:
            hidden_states: Input tensor of shape (batch_size, seq_len, hidden_size)
            attention_mask: Optional mask of shape (batch_size, seq_len)
            position_embeddings: Optional position embeddings

        Returns:
            Tuple of (output_states, attention_weights)
        """
        batch_size, seq_len, _ = hidden_states.shape

        # Apply position embeddings if provided
        if position_embeddings is not None:
            hidden_states = hidden_states + position_embeddings

        # Project to Q, K, V
        q = self.q_proj(hidden_states)
        k = self.k_proj(hidden_states)
        v = self.v_proj(hidden_states)

        # Reshape for multi-head attention
        q = q.view(batch_size, seq_len, self.num_heads, self.head_dim)
        k = k.view(batch_size, seq_len, self.num_heads, self.head_dim)
        v = v.view(batch_size, seq_len, self.num_heads, self.head_dim)

        if self.use_flash_attention and hidden_states.is_cuda:
            # Use Flash Attention for efficiency (only on CUDA)
            attn_output = flash_attn_func(
                q, k, v,
                dropout_p=self.dropout if self.training else 0.0,
                causal=False,  # Non-autoregressive
            )
            attn_weights = None  # Flash attention doesn't return weights
        else:
            # Standard attention computation
            q = q.transpose(1, 2)  # (batch_size, num_heads, seq_len, head_dim)
            k = k.transpose(1, 2)
            v = v.transpose(1, 2)

            # Compute attention scores
            attn_scores = torch.matmul(q, k.transpose(-2, -1)) / math.sqrt(self.head_dim)

            # Apply attention mask if provided
            if attention_mask is not None:
                # Expand mask for multi-head attention
                mask = attention_mask.unsqueeze(1).unsqueeze(2)
                mask = mask.expand(batch_size, self.num_heads, seq_len, seq_len)
                attn_scores = attn_scores.masked_fill(mask == 0, float('-inf'))

            # Apply softmax and dropout
            attn_weights = F.softmax(attn_scores, dim=-1)
            attn_weights = self.dropout_layer(attn_weights)

            # Apply attention to values
            attn_output = torch.matmul(attn_weights, v)
            attn_output = attn_output.transpose(1, 2)  # Back to (batch_size, seq_len, num_heads, head_dim)

        # Reshape and project output
        attn_output = attn_output.contiguous().view(batch_size, seq_len, self.hidden_size)
        output = self.o_proj(attn_output)

        return output, attn_weights


class CrossAttention(nn.Module):
    """
    Cross-attention mechanism for integrating ThinkerModule outputs with MainLLM.
    """

    def __init__(
        self,
        query_dim: int,
        key_value_dim: int,
        num_heads: int,
        dropout: float = 0.1,
    ):
        super().__init__()
        self.query_dim = query_dim
        self.key_value_dim = key_value_dim
        self.num_heads = num_heads
        self.head_dim = query_dim // num_heads
        self.dropout = dropout

        assert query_dim % num_heads == 0, "query_dim must be divisible by num_heads"

        self.q_proj = nn.Linear(query_dim, query_dim, bias=False)
        self.k_proj = nn.Linear(key_value_dim, query_dim, bias=False)
        self.v_proj = nn.Linear(key_value_dim, query_dim, bias=False)
        self.o_proj = nn.Linear(query_dim, query_dim, bias=False)

        self.dropout_layer = nn.Dropout(dropout)

    def forward(
        self,
        query_states: torch.Tensor,
        key_value_states: torch.Tensor,
        attention_mask: Optional[torch.Tensor] = None,
    ) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Forward pass for cross-attention.

        Args:
            query_states: Query tensor from MainLLM (batch_size, query_len, query_dim)
            key_value_states: Key/Value tensor from ThinkerModule (batch_size, kv_len, key_value_dim)
            attention_mask: Optional mask for key/value states

        Returns:
            Tuple of (output_states, attention_weights)
        """
        batch_size, query_len, _ = query_states.shape
        kv_len = key_value_states.shape[1]

        # Project to Q, K, V
        q = self.q_proj(query_states)
        k = self.k_proj(key_value_states)
        v = self.v_proj(key_value_states)

        # Reshape for multi-head attention
        q = q.view(batch_size, query_len, self.num_heads, self.head_dim).transpose(1, 2)
        k = k.view(batch_size, kv_len, self.num_heads, self.head_dim).transpose(1, 2)
        v = v.view(batch_size, kv_len, self.num_heads, self.head_dim).transpose(1, 2)

        # Compute attention scores
        attn_scores = torch.matmul(q, k.transpose(-2, -1)) / math.sqrt(self.head_dim)

        # Apply attention mask if provided
        if attention_mask is not None:
            # Expand mask for multi-head attention
            mask = attention_mask.unsqueeze(1).unsqueeze(2)
            mask = mask.expand(batch_size, self.num_heads, query_len, kv_len)
            attn_scores = attn_scores.masked_fill(mask == 0, float('-inf'))

        # Apply softmax and dropout
        attn_weights = F.softmax(attn_scores, dim=-1)
        attn_weights = self.dropout_layer(attn_weights)

        # Apply attention to values
        attn_output = torch.matmul(attn_weights, v)
        attn_output = attn_output.transpose(1, 2).contiguous()
        attn_output = attn_output.view(batch_size, query_len, self.query_dim)

        # Project output
        output = self.o_proj(attn_output)

        return output, attn_weights


class CausalSelfAttention(nn.Module):
    """
    Standard causal self-attention for autoregressive MainLLM.
    """

    def __init__(
        self,
        hidden_size: int,
        num_heads: int,
        dropout: float = 0.1,
        use_flash_attention: bool = True,
    ):
        super().__init__()
        self.hidden_size = hidden_size
        self.num_heads = num_heads
        self.head_dim = hidden_size // num_heads
        self.dropout = dropout
        self.use_flash_attention = use_flash_attention and FLASH_ATTENTION_AVAILABLE

        assert hidden_size % num_heads == 0, "hidden_size must be divisible by num_heads"

        self.q_proj = nn.Linear(hidden_size, hidden_size, bias=False)
        self.k_proj = nn.Linear(hidden_size, hidden_size, bias=False)
        self.v_proj = nn.Linear(hidden_size, hidden_size, bias=False)
        self.o_proj = nn.Linear(hidden_size, hidden_size, bias=False)

        self.dropout_layer = nn.Dropout(dropout)

    def forward(
        self,
        hidden_states: torch.Tensor,
        attention_mask: Optional[torch.Tensor] = None,
        position_embeddings: Optional[torch.Tensor] = None,
    ) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Forward pass for causal self-attention.

        Args:
            hidden_states: Input tensor of shape (batch_size, seq_len, hidden_size)
            attention_mask: Optional mask of shape (batch_size, seq_len)
            position_embeddings: Optional position embeddings

        Returns:
            Tuple of (output_states, attention_weights)
        """
        batch_size, seq_len, _ = hidden_states.shape

        # Apply position embeddings if provided
        if position_embeddings is not None:
            hidden_states = hidden_states + position_embeddings

        # Project to Q, K, V
        q = self.q_proj(hidden_states)
        k = self.k_proj(hidden_states)
        v = self.v_proj(hidden_states)

        # Reshape for multi-head attention
        q = q.view(batch_size, seq_len, self.num_heads, self.head_dim)
        k = k.view(batch_size, seq_len, self.num_heads, self.head_dim)
        v = v.view(batch_size, seq_len, self.num_heads, self.head_dim)

        if self.use_flash_attention and hidden_states.is_cuda:
            # Use Flash Attention with causal masking (only on CUDA)
            attn_output = flash_attn_func(
                q, k, v,
                dropout_p=self.dropout if self.training else 0.0,
                causal=True,  # Autoregressive
            )
            attn_weights = None
        else:
            # Standard causal attention computation
            q = q.transpose(1, 2)
            k = k.transpose(1, 2)
            v = v.transpose(1, 2)

            # Compute attention scores
            attn_scores = torch.matmul(q, k.transpose(-2, -1)) / math.sqrt(self.head_dim)

            # Apply causal mask
            causal_mask = torch.tril(torch.ones(seq_len, seq_len, device=hidden_states.device))
            attn_scores = attn_scores.masked_fill(causal_mask == 0, float('-inf'))

            # Apply attention mask if provided
            if attention_mask is not None:
                mask = attention_mask.unsqueeze(1).unsqueeze(2)
                mask = mask.expand(batch_size, self.num_heads, seq_len, seq_len)
                attn_scores = attn_scores.masked_fill(mask == 0, float('-inf'))

            # Apply softmax and dropout
            attn_weights = F.softmax(attn_scores, dim=-1)
            attn_weights = self.dropout_layer(attn_weights)

            # Apply attention to values
            attn_output = torch.matmul(attn_weights, v)
            attn_output = attn_output.transpose(1, 2)

        # Reshape and project output
        attn_output = attn_output.contiguous().view(batch_size, seq_len, self.hidden_size)
        output = self.o_proj(attn_output)

        return output, attn_weights
