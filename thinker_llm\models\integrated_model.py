"""
IntegratedThinkerLLM: Complete model combining all components.
"""

import torch
import torch.nn as nn
from typing import Optional, Dict, Any, Tuple, Union
import warnings

from .thinker_module import ThinkerModule
from .main_llm import MainLLM
from .decision_mechanism import DecisionMechanism
from ..utils.config import ModelConfig


class IntegratedThinkerLLM(nn.Module):
    """
    Complete ThinkerLLM model that integrates:
    1. DecisionMechanism - determines when thinking is needed
    2. ThinkerModule - performs non-autoregressive reasoning
    3. MainLLM - generates final autoregressive output
    """

    def __init__(self, config: ModelConfig):
        super().__init__()
        self.config = config

        # Initialize components
        self.decision_mechanism = DecisionMechanism(config)
        self.thinker_module = ThinkerModule(config)
        self.main_llm = MainLLM(config)

        # Training flags
        self.training_mode = "joint"  # "joint", "separate", "frozen"

    def set_training_mode(self, mode: str):
        """
        Set training mode for different components.

        Args:
            mode: "joint" (train all), "separate" (train components separately),
                  "frozen" (freeze some components)
        """
        self.training_mode = mode

        if mode == "joint":
            # Train all components
            for param in self.parameters():
                param.requires_grad = True
        elif mode == "frozen_llm":
            # Freeze main LLM, train thinker and decision
            for param in self.main_llm.parameters():
                param.requires_grad = False
            for param in self.thinker_module.parameters():
                param.requires_grad = True
            for param in self.decision_mechanism.parameters():
                param.requires_grad = True
        elif mode == "frozen_thinker":
            # Freeze thinker, train LLM and decision
            for param in self.thinker_module.parameters():
                param.requires_grad = False
            for param in self.main_llm.parameters():
                param.requires_grad = True
            for param in self.decision_mechanism.parameters():
                param.requires_grad = True
        elif mode == "frozen_decision":
            # Freeze decision mechanism, train others
            for param in self.decision_mechanism.parameters():
                param.requires_grad = False
            for param in self.thinker_module.parameters():
                param.requires_grad = True
            for param in self.main_llm.parameters():
                param.requires_grad = True

    def forward(
        self,
        input_ids: torch.Tensor,
        attention_mask: Optional[torch.Tensor] = None,
        labels: Optional[torch.Tensor] = None,
        force_thinking: bool = False,
        disable_thinking: bool = False,
        return_dict: bool = True,
        return_thoughts: bool = False,
    ) -> Dict[str, torch.Tensor]:
        """
        Forward pass through the complete ThinkerLLM model.

        Args:
            input_ids: Input token IDs (batch_size, seq_len)
            attention_mask: Optional attention mask
            labels: Optional labels for computing loss
            force_thinking: Force thinking regardless of decision
            disable_thinking: Disable thinking regardless of decision
            return_dict: Whether to return a dictionary
            return_thoughts: Whether to return thought sequences

        Returns:
            Dictionary containing model outputs and losses
        """
        batch_size = input_ids.size(0)
        device = input_ids.device

        # Step 1: Decision mechanism determines if thinking is needed
        should_think_mask, decision_info = self.decision_mechanism.should_think(
            input_ids=input_ids,
            attention_mask=attention_mask,
            force_thinking=force_thinking,
            disable_thinking=disable_thinking,
        )

        # Initialize outputs
        reasoning_states = None
        reasoning_mask = None
        thought_logits = None
        thought_ids = None
        thinker_outputs = None

        # Step 2: Apply ThinkerModule for samples that need thinking
        if should_think_mask.any():
            # Get indices of samples that need thinking
            thinking_indices = should_think_mask.nonzero(as_tuple=True)[0]

            if len(thinking_indices) > 0:
                # Extract samples that need thinking
                thinking_input_ids = input_ids[thinking_indices]
                thinking_attention_mask = attention_mask[thinking_indices] if attention_mask is not None else None

                # Apply ThinkerModule
                thinker_outputs = self.thinker_module(
                    input_ids=thinking_input_ids,
                    attention_mask=thinking_attention_mask,
                    return_dict=True,
                )

                # Initialize full batch reasoning states
                reasoning_states = torch.zeros(
                    batch_size,
                    thinker_outputs["reasoning_states"].size(1),
                    thinker_outputs["reasoning_states"].size(2),
                    device=device,
                    dtype=thinker_outputs["reasoning_states"].dtype,
                )

                # Fill in reasoning states for thinking samples
                reasoning_states[thinking_indices] = thinker_outputs["reasoning_states"]

                # Create reasoning mask
                reasoning_mask = torch.zeros(batch_size, reasoning_states.size(1), device=device, dtype=torch.bool)
                if thinking_attention_mask is not None:
                    reasoning_mask[thinking_indices] = thinking_attention_mask.bool()
                else:
                    reasoning_mask[thinking_indices] = True

                # Store thought information if requested
                if return_thoughts:
                    thought_logits = torch.zeros(
                        batch_size,
                        thinker_outputs["thought_logits"].size(1),
                        thinker_outputs["thought_logits"].size(2),
                        device=device,
                        dtype=thinker_outputs["thought_logits"].dtype,
                    )
                    thought_logits[thinking_indices] = thinker_outputs["thought_logits"]

        # Step 3: Apply MainLLM with optional reasoning states
        llm_outputs = self.main_llm(
            input_ids=input_ids,
            attention_mask=attention_mask,
            reasoning_states=reasoning_states,
            reasoning_mask=reasoning_mask,
            labels=labels,
            return_dict=True,
        )

        # Combine outputs
        outputs = {
            "logits": llm_outputs["logits"],
            "hidden_states": llm_outputs["hidden_states"],
            "loss": llm_outputs["loss"],
            "decision_info": decision_info,
            "should_think_mask": should_think_mask,
            "reasoning_states": reasoning_states,
        }

        if return_thoughts and thought_logits is not None:
            outputs["thought_logits"] = thought_logits
            outputs["thinker_outputs"] = thinker_outputs

        if not return_dict:
            return tuple(outputs.values())

        return outputs

    def generate(
        self,
        input_ids: torch.Tensor,
        attention_mask: Optional[torch.Tensor] = None,
        max_new_tokens: int = 512,
        temperature: float = 0.7,
        top_p: float = 0.9,
        top_k: int = 50,
        do_sample: bool = True,
        num_beams: int = 1,
        pad_token_id: Optional[int] = None,
        eos_token_id: Optional[int] = None,
        force_thinking: bool = False,
        disable_thinking: bool = False,
        return_thoughts: bool = False,
        thinking_temperature: float = 0.8,
    ) -> Dict[str, torch.Tensor]:
        """
        Generate text using the complete ThinkerLLM model.

        Args:
            input_ids: Input token IDs
            attention_mask: Optional attention mask
            max_new_tokens: Maximum number of new tokens to generate
            temperature: Sampling temperature for main generation
            top_p: Top-p sampling parameter
            top_k: Top-k sampling parameter
            do_sample: Whether to use sampling
            num_beams: Number of beams for beam search
            pad_token_id: Padding token ID
            eos_token_id: End-of-sequence token ID
            force_thinking: Force thinking regardless of decision
            disable_thinking: Disable thinking regardless of decision
            return_thoughts: Whether to return thought sequences
            thinking_temperature: Temperature for thought generation

        Returns:
            Dictionary containing generated outputs
        """
        self.eval()

        with torch.no_grad():
            # Step 1: Decision mechanism
            should_think_mask, decision_info = self.decision_mechanism.should_think(
                input_ids=input_ids,
                attention_mask=attention_mask,
                force_thinking=force_thinking,
                disable_thinking=disable_thinking,
            )

            # Initialize outputs
            reasoning_states = None
            reasoning_mask = None
            thought_ids = None

            # Step 2: Generate thoughts if needed
            if should_think_mask.any():
                thinking_indices = should_think_mask.nonzero(as_tuple=True)[0]

                if len(thinking_indices) > 0:
                    thinking_input_ids = input_ids[thinking_indices]
                    thinking_attention_mask = attention_mask[thinking_indices] if attention_mask is not None else None

                    # Generate thoughts
                    batch_thought_ids, batch_reasoning_states = self.thinker_module.generate_thoughts(
                        input_ids=thinking_input_ids,
                        attention_mask=thinking_attention_mask,
                        max_new_tokens=min(max_new_tokens // 2, 256),  # Limit thinking tokens
                        temperature=thinking_temperature,
                        top_p=top_p,
                        do_sample=do_sample,
                    )

                    # Initialize full batch tensors
                    batch_size = input_ids.size(0)
                    device = input_ids.device

                    reasoning_states = torch.zeros(
                        batch_size,
                        batch_reasoning_states.size(1),
                        batch_reasoning_states.size(2),
                        device=device,
                        dtype=batch_reasoning_states.dtype,
                    )
                    reasoning_states[thinking_indices] = batch_reasoning_states

                    reasoning_mask = torch.zeros(batch_size, reasoning_states.size(1), device=device, dtype=torch.bool)
                    if thinking_attention_mask is not None:
                        reasoning_mask[thinking_indices] = thinking_attention_mask.bool()
                    else:
                        reasoning_mask[thinking_indices] = True

                    if return_thoughts:
                        thought_ids = torch.zeros(
                            batch_size,
                            batch_thought_ids.size(1),
                            device=device,
                            dtype=batch_thought_ids.dtype,
                        )
                        thought_ids[thinking_indices] = batch_thought_ids

            # Step 3: Generate final response
            generated_ids = self.main_llm.generate(
                input_ids=input_ids,
                attention_mask=attention_mask,
                reasoning_states=reasoning_states,
                reasoning_mask=reasoning_mask,
                max_new_tokens=max_new_tokens,
                temperature=temperature,
                top_p=top_p,
                top_k=top_k,
                do_sample=do_sample,
                num_beams=num_beams,
                pad_token_id=pad_token_id,
                eos_token_id=eos_token_id,
            )

        # Prepare outputs
        outputs = {
            "generated_ids": generated_ids,
            "decision_info": decision_info,
            "should_think_mask": should_think_mask,
        }

        if return_thoughts and thought_ids is not None:
            outputs["thought_ids"] = thought_ids

        return outputs

    def compute_loss(
        self,
        input_ids: torch.Tensor,
        attention_mask: Optional[torch.Tensor] = None,
        labels: Optional[torch.Tensor] = None,
        thinking_labels: Optional[torch.Tensor] = None,
        decision_labels: Optional[torch.Tensor] = None,
        loss_weights: Optional[Dict[str, float]] = None,
    ) -> Dict[str, torch.Tensor]:
        """
        Compute combined loss for all components.

        Args:
            input_ids: Input token IDs
            attention_mask: Optional attention mask
            labels: Labels for main LLM
            thinking_labels: Labels for thinking decisions
            decision_labels: Labels for decision mechanism
            loss_weights: Weights for different loss components

        Returns:
            Dictionary containing individual and combined losses
        """
        if loss_weights is None:
            loss_weights = {
                "llm": self.config.llm_loss_weight if hasattr(self.config, 'llm_loss_weight') else 1.0,
                "thinker": self.config.thinker_loss_weight if hasattr(self.config, 'thinker_loss_weight') else 0.5,
                "decision": self.config.decision_loss_weight if hasattr(self.config, 'decision_loss_weight') else 0.3,
            }

        # Forward pass
        outputs = self.forward(
            input_ids=input_ids,
            attention_mask=attention_mask,
            labels=labels,
            return_thoughts=True,
        )

        losses = {}

        # Main LLM loss
        if outputs["loss"] is not None:
            losses["llm_loss"] = outputs["loss"]

        # Thinker loss (if thinking was applied)
        if "thinker_outputs" in outputs and thinking_labels is not None:
            thinker_outputs = outputs["thinker_outputs"]
            thought_logits = thinker_outputs["thought_logits"]

            # Compute thinking loss
            shift_logits = thought_logits[..., :-1, :].contiguous()
            shift_labels = thinking_labels[..., 1:].contiguous()

            loss_fct = nn.CrossEntropyLoss()
            losses["thinker_loss"] = loss_fct(
                shift_logits.view(-1, shift_logits.size(-1)),
                shift_labels.view(-1)
            )

        # Decision loss
        if decision_labels is not None:
            decision_probs = outputs["decision_info"]["thinking_probability"]
            loss_fct = nn.BCELoss()
            losses["decision_loss"] = loss_fct(decision_probs, decision_labels.float())

        # Combined loss
        total_loss = torch.tensor(0.0, device=input_ids.device)
        for loss_name, loss_value in losses.items():
            weight_key = loss_name.replace("_loss", "")
            weight = loss_weights.get(weight_key, 1.0)
            total_loss += weight * loss_value

        losses["total_loss"] = total_loss

        return losses

    def get_model_info(self) -> Dict[str, Any]:
        """Get information about the model architecture."""
        def count_parameters(model):
            return sum(p.numel() for p in model.parameters() if p.requires_grad)

        return {
            "total_parameters": count_parameters(self),
            "decision_parameters": count_parameters(self.decision_mechanism),
            "thinker_parameters": count_parameters(self.thinker_module),
            "llm_parameters": count_parameters(self.main_llm),
            "config": self.config.__dict__,
            "training_mode": self.training_mode,
        }
