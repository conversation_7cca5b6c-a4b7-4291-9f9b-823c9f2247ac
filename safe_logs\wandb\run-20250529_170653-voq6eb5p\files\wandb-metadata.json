{"os": "Windows-11-10.0.26100-SP0", "python": "CPython 3.12.9", "startedAt": "2025-05-29T10:06:53.447031Z", "program": "D:\\vlinhd11\\Projects\\LCMS_LLM\\MOE-LLM\\geneticAI\\v4\\scripts\\train_cpu_safe.py", "codePath": "scripts\\train_cpu_safe.py", "email": "<EMAIL>", "root": "./safe_logs", "host": "VLINHD11-WORK", "executable": "D:\\.conda\\envs\\tts\\python.exe", "codePathLocal": "scripts\\train_cpu_safe.py", "cpu_count": 10, "cpu_count_logical": 20, "gpu": "NVIDIA GeForce RTX 4070", "gpu_count": 1, "disk": {"/": {"total": "540765319168", "used": "424556658688"}}, "memory": {"total": "34165837824"}, "cpu": {"count": 10, "countLogical": 20}, "gpu_nvidia": [{"name": "NVIDIA GeForce RTX 4070", "memoryTotal": "12878610432", "cudaCores": 5888, "architecture": "Ada"}], "cudaVersion": "12.6"}