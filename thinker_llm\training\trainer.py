"""
Main trainer for ThinkerLLM.
"""

import torch
import torch.nn as nn
from torch.utils.data import DataLoader
from typing import Dict, Any, Optional, List, Tuple
import os
import json
import logging
from pathlib import Path
from tqdm import tqdm
import wandb
from transformers import get_linear_schedule_with_warmup

from ..models.integrated_model import IntegratedThinkerLLM
from ..utils.config import ModelConfig, TrainingConfig
from .losses import CombinedLoss
from .data_loader import ThinkerDataLoader
from .optimizers import get_optimizer, get_scheduler


class ThinkerLLMTrainer:
    """
    Trainer for ThinkerLLM with support for joint and separate training strategies.
    """
    
    def __init__(
        self,
        model: IntegratedThinkerLLM,
        train_dataloader: DataLoader,
        val_dataloader: Optional[DataLoader] = None,
        config: TrainingConfig = None,
        model_config: ModelConfig = None,
    ):
        self.model = model
        self.train_dataloader = train_dataloader
        self.val_dataloader = val_dataloader
        self.config = config or TrainingConfig()
        self.model_config = model_config or ModelConfig()
        
        # Setup device
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.model.to(self.device)
        
        # Setup loss function
        self.loss_fn = CombinedLoss(
            vocab_size=self.model_config.vocab_size,
            llm_weight=self.config.llm_loss_weight,
            thinker_weight=self.config.thinker_loss_weight,
            decision_weight=self.config.decision_loss_weight,
        )
        
        # Setup optimizer and scheduler
        self.optimizer = get_optimizer(
            model=self.model,
            optimizer_name=self.config.optimizer,
            learning_rate=self.config.learning_rate,
            weight_decay=self.config.weight_decay,
        )
        
        self.scheduler = get_scheduler(
            optimizer=self.optimizer,
            scheduler_name=self.config.scheduler,
            num_training_steps=len(self.train_dataloader) * self.config.num_epochs,
            warmup_steps=self.config.warmup_steps,
        )
        
        # Training state
        self.global_step = 0
        self.epoch = 0
        self.best_val_loss = float('inf')
        
        # Setup logging
        self.setup_logging()
        
        # Setup output directories
        self.setup_directories()
        
    def setup_logging(self):
        """Setup logging configuration."""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(os.path.join(self.config.logging_dir, 'training.log')),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
        # Initialize wandb if available
        try:
            wandb.init(
                project="thinker-llm",
                config=self.config.__dict__,
                dir=self.config.logging_dir,
            )
            self.use_wandb = True
        except Exception as e:
            self.logger.warning(f"Failed to initialize wandb: {e}")
            self.use_wandb = False
    
    def setup_directories(self):
        """Setup output directories."""
        os.makedirs(self.config.output_dir, exist_ok=True)
        os.makedirs(self.config.logging_dir, exist_ok=True)
        os.makedirs(self.config.cache_dir, exist_ok=True)
    
    def train_epoch(self) -> Dict[str, float]:
        """Train for one epoch."""
        self.model.train()
        total_loss = 0.0
        loss_components = {}
        
        progress_bar = tqdm(
            self.train_dataloader,
            desc=f"Epoch {self.epoch + 1}/{self.config.num_epochs}",
            leave=False,
        )
        
        for step, batch in enumerate(progress_bar):
            # Move batch to device
            batch = {k: v.to(self.device) if isinstance(v, torch.Tensor) else v 
                    for k, v in batch.items()}
            
            # Forward pass
            outputs = self.model(
                input_ids=batch["input_ids"],
                attention_mask=batch["attention_mask"],
                labels=batch["labels"],
                return_thoughts=True,
            )
            
            # Compute loss
            loss_dict = self.loss_fn(
                model_outputs=outputs,
                labels=batch["labels"],
                thinking_labels=batch.get("thinking_labels"),
                decision_labels=batch.get("requires_thinking"),
            )
            
            loss = loss_dict["total_loss"]
            
            # Backward pass
            if self.config.gradient_accumulation_steps > 1:
                loss = loss / self.config.gradient_accumulation_steps
            
            loss.backward()
            
            # Update weights
            if (step + 1) % self.config.gradient_accumulation_steps == 0:
                # Gradient clipping
                if self.config.max_grad_norm > 0:
                    torch.nn.utils.clip_grad_norm_(
                        self.model.parameters(),
                        self.config.max_grad_norm
                    )
                
                self.optimizer.step()
                self.scheduler.step()
                self.optimizer.zero_grad()
                
                self.global_step += 1
            
            # Accumulate losses
            total_loss += loss.item()
            for key, value in loss_dict.items():
                if key not in loss_components:
                    loss_components[key] = 0.0
                loss_components[key] += value.item()
            
            # Update progress bar
            progress_bar.set_postfix({
                "loss": f"{loss.item():.4f}",
                "lr": f"{self.scheduler.get_last_lr()[0]:.2e}",
            })
            
            # Logging
            if self.global_step % self.config.logging_steps == 0:
                self.log_metrics({
                    "train/loss": loss.item(),
                    "train/learning_rate": self.scheduler.get_last_lr()[0],
                    "train/global_step": self.global_step,
                })
            
            # Evaluation
            if (self.val_dataloader is not None and 
                self.global_step % self.config.eval_steps == 0):
                val_metrics = self.evaluate()
                self.log_metrics(val_metrics)
                self.model.train()  # Return to training mode
            
            # Save checkpoint
            if self.global_step % self.config.save_steps == 0:
                self.save_checkpoint()
        
        # Average losses over epoch
        num_batches = len(self.train_dataloader)
        epoch_metrics = {
            "train/epoch_loss": total_loss / num_batches,
        }
        
        for key, value in loss_components.items():
            epoch_metrics[f"train/epoch_{key}"] = value / num_batches
        
        return epoch_metrics
    
    def evaluate(self) -> Dict[str, float]:
        """Evaluate on validation set."""
        if self.val_dataloader is None:
            return {}
        
        self.model.eval()
        total_loss = 0.0
        loss_components = {}
        
        with torch.no_grad():
            for batch in tqdm(self.val_dataloader, desc="Evaluating", leave=False):
                # Move batch to device
                batch = {k: v.to(self.device) if isinstance(v, torch.Tensor) else v 
                        for k, v in batch.items()}
                
                # Forward pass
                outputs = self.model(
                    input_ids=batch["input_ids"],
                    attention_mask=batch["attention_mask"],
                    labels=batch["labels"],
                    return_thoughts=True,
                )
                
                # Compute loss
                loss_dict = self.loss_fn(
                    model_outputs=outputs,
                    labels=batch["labels"],
                    thinking_labels=batch.get("thinking_labels"),
                    decision_labels=batch.get("requires_thinking"),
                )
                
                # Accumulate losses
                total_loss += loss_dict["total_loss"].item()
                for key, value in loss_dict.items():
                    if key not in loss_components:
                        loss_components[key] = 0.0
                    loss_components[key] += value.item()
        
        # Average losses
        num_batches = len(self.val_dataloader)
        val_metrics = {
            "val/loss": total_loss / num_batches,
        }
        
        for key, value in loss_components.items():
            val_metrics[f"val/{key}"] = value / num_batches
        
        # Check if this is the best validation loss
        current_val_loss = val_metrics["val/loss"]
        if current_val_loss < self.best_val_loss:
            self.best_val_loss = current_val_loss
            self.save_checkpoint(is_best=True)
        
        return val_metrics
    
    def train(self):
        """Main training loop."""
        self.logger.info("Starting training...")
        self.logger.info(f"Total epochs: {self.config.num_epochs}")
        self.logger.info(f"Total steps: {len(self.train_dataloader) * self.config.num_epochs}")
        
        for epoch in range(self.config.num_epochs):
            self.epoch = epoch
            
            # Train epoch
            epoch_metrics = self.train_epoch()
            
            # Log epoch metrics
            self.log_metrics(epoch_metrics)
            
            # Evaluate
            if self.val_dataloader is not None:
                val_metrics = self.evaluate()
                self.log_metrics(val_metrics)
            
            self.logger.info(
                f"Epoch {epoch + 1}/{self.config.num_epochs} completed. "
                f"Train loss: {epoch_metrics['train/epoch_loss']:.4f}"
            )
        
        self.logger.info("Training completed!")
        
        # Save final checkpoint
        self.save_checkpoint(is_final=True)
    
    def log_metrics(self, metrics: Dict[str, float]):
        """Log metrics to wandb and console."""
        if self.use_wandb:
            wandb.log(metrics, step=self.global_step)
        
        # Log to console
        for key, value in metrics.items():
            self.logger.info(f"{key}: {value:.4f}")
    
    def save_checkpoint(self, is_best: bool = False, is_final: bool = False):
        """Save model checkpoint."""
        checkpoint = {
            "model_state_dict": self.model.state_dict(),
            "optimizer_state_dict": self.optimizer.state_dict(),
            "scheduler_state_dict": self.scheduler.state_dict(),
            "global_step": self.global_step,
            "epoch": self.epoch,
            "best_val_loss": self.best_val_loss,
            "config": self.config.__dict__,
            "model_config": self.model_config.__dict__,
        }
        
        # Save regular checkpoint
        if is_final:
            checkpoint_path = os.path.join(self.config.output_dir, "final_checkpoint.pt")
        elif is_best:
            checkpoint_path = os.path.join(self.config.output_dir, "best_checkpoint.pt")
        else:
            checkpoint_path = os.path.join(
                self.config.output_dir, 
                f"checkpoint_step_{self.global_step}.pt"
            )
        
        torch.save(checkpoint, checkpoint_path)
        self.logger.info(f"Checkpoint saved: {checkpoint_path}")
        
        # Clean up old checkpoints
        if not is_best and not is_final:
            self._cleanup_checkpoints()
    
    def _cleanup_checkpoints(self):
        """Remove old checkpoints to save space."""
        checkpoint_dir = Path(self.config.output_dir)
        checkpoints = list(checkpoint_dir.glob("checkpoint_step_*.pt"))
        
        if len(checkpoints) > self.config.save_total_limit:
            # Sort by step number and remove oldest
            checkpoints.sort(key=lambda x: int(x.stem.split("_")[-1]))
            for checkpoint in checkpoints[:-self.config.save_total_limit]:
                checkpoint.unlink()
                self.logger.info(f"Removed old checkpoint: {checkpoint}")
    
    def load_checkpoint(self, checkpoint_path: str):
        """Load model checkpoint."""
        checkpoint = torch.load(checkpoint_path, map_location=self.device)
        
        self.model.load_state_dict(checkpoint["model_state_dict"])
        self.optimizer.load_state_dict(checkpoint["optimizer_state_dict"])
        self.scheduler.load_state_dict(checkpoint["scheduler_state_dict"])
        self.global_step = checkpoint["global_step"]
        self.epoch = checkpoint["epoch"]
        self.best_val_loss = checkpoint["best_val_loss"]
        
        self.logger.info(f"Checkpoint loaded: {checkpoint_path}")
        self.logger.info(f"Resuming from step {self.global_step}, epoch {self.epoch}")
    
    def set_training_mode(self, mode: str):
        """Set training mode for different components."""
        self.model.set_training_mode(mode)
        self.logger.info(f"Training mode set to: {mode}")
