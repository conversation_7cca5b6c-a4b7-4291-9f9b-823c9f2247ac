"""
ThinkerLLM: Large Language Model with Integrated ThinkerModule

A modular implementation of an LLM architecture that combines traditional
autoregressive generation with non-autoregressive reasoning capabilities.
"""

__version__ = "0.1.0"
__author__ = "Your Name"

from .models import (
    ThinkerModule,
    MainLLM,
    DecisionMechanism,
    IntegratedThinkerLLM,
)

from .training import (
    ThinkerLLMTrainer,
    ThinkerLoss,
    LLMLoss,
    DecisionLoss,
)

from .inference import (
    ThinkerLLMPipeline,
    InferenceConfig,
)

from .utils import (
    ModelConfig,
    TrainingConfig,
    load_model,
    save_model,
)

__all__ = [
    "ThinkerModule",
    "MainLLM", 
    "DecisionMechanism",
    "IntegratedThinkerLLM",
    "ThinkerLLMTrainer",
    "ThinkerLoss",
    "LLMLoss", 
    "DecisionLoss",
    "ThinkerLLMPipeline",
    "InferenceConfig",
    "ModelConfig",
    "TrainingConfig",
    "load_model",
    "save_model",
]
