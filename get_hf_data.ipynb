from datasets import load_dataset
import json
from tqdm import tqdm

# Load dataset in streaming mode
dataset = load_dataset("KingNish/reasoning-base-20k", streaming=True)

# Open output file
with open("test_data/train.jsonl", "w", encoding="utf-8") as f:
    # Iterate through dataset
    for record in tqdm(dataset["train"]):
        # Write each record as a JSON line
        data = {
            "instruction": record["user"],
            "thinking": record["reasoning"],
            "output": record["assistant"],
            "requires_thinking": True,
        }
        json.dump(data, f, ensure_ascii=False)
        f.write("\n")