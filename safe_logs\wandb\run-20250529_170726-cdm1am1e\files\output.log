INFO:__main__:Trainer created successfully
INFO:__main__:Starting training...
INFO:thinker_llm.training.trainer:Starting training...
INFO:thinker_llm.training.trainer:Total epochs: 1
INFO:thinker_llm.training.trainer:Total steps: 40
ERROR:__main__:Training failed: CUDA error: device-side assert triggered                                                                                    
CUDA kernel errors might be asynchronously reported at some other API call, so the stacktrace below might be incorrect.
For debugging consider passing CUDA_LAUNCH_BLOCKING=1
Compile with `TORCH_USE_CUDA_DSA` to enable device-side assertions.

INFO:__main__:This might be due to insufficient memory or CUDA issues
INFO:__main__:Try running with --force-cpu or reducing batch size
Traceback (most recent call last):
  File "D:\vlinhd11\Projects\LCMS_LLM\MOE-LLM\geneticAI\v4\scripts\train_cpu_safe.py", line 337, in <module>
    main()
  File "D:\vlinhd11\Projects\LCMS_LLM\MOE-LLM\geneticAI\v4\scripts\train_cpu_safe.py", line 310, in main
    trainer.train()
  File "D:\vlinhd11\Projects\LCMS_LLM\MOE-LLM\geneticAI\v4\thinker_llm\training\trainer.py", line 275, in train
    epoch_metrics = self.train_epoch()
                    ^^^^^^^^^^^^^^^^^^
  File "D:\vlinhd11\Projects\LCMS_LLM\MOE-LLM\geneticAI\v4\thinker_llm\training\trainer.py", line 129, in train_epoch
    outputs = self.model(
              ^^^^^^^^^^^
  File "D:\.conda\envs\tts\Lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\.conda\envs\tts\Lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\vlinhd11\Projects\LCMS_LLM\MOE-LLM\geneticAI\v4\thinker_llm\models\integrated_model.py", line 104, in forward
    should_think_mask, decision_info = self.decision_mechanism.should_think(
                                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\vlinhd11\Projects\LCMS_LLM\MOE-LLM\geneticAI\v4\thinker_llm\models\decision_mechanism.py", line 304, in should_think
    decision_info = self.forward(input_ids, attention_mask)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\vlinhd11\Projects\LCMS_LLM\MOE-LLM\geneticAI\v4\thinker_llm\models\decision_mechanism.py", line 235, in forward
    text_features = self.text_feature_extractor(input_ids, attention_mask)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\.conda\envs\tts\Lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\.conda\envs\tts\Lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\vlinhd11\Projects\LCMS_LLM\MOE-LLM\geneticAI\v4\thinker_llm\models\decision_mechanism.py", line 37, in forward
    embeds = embeds * mask
             ~~~~~~~^~~~~~
RuntimeError: CUDA error: device-side assert triggered
CUDA kernel errors might be asynchronously reported at some other API call, so the stacktrace below might be incorrect.
For debugging consider passing CUDA_LAUNCH_BLOCKING=1
Compile with `TORCH_USE_CUDA_DSA` to enable device-side assertions.
