"""
Data loading and preprocessing for ThinkerLLM training.
"""

import torch
from torch.utils.data import Dataset, DataLoader
from typing import Dict, List, Optional, Any, Tuple, Union
import json
import random
from pathlib import Path
from transformers import AutoTokenizer
import numpy as np


class ThinkerDataset(Dataset):
    """
    Dataset for ThinkerLLM training that handles both thinking and non-thinking samples.
    """

    def __init__(
        self,
        data_path: str,
        tokenizer: AutoTokenizer,
        max_length: int = 2048,
        thinking_ratio: float = 0.3,
        include_thoughts: bool = True,
        split: str = "train",
    ):
        self.data_path = Path(data_path)
        self.tokenizer = tokenizer
        self.max_length = max_length
        self.thinking_ratio = thinking_ratio
        self.include_thoughts = include_thoughts
        self.split = split

        # Load data
        self.samples = self._load_data()

        # Ensure special tokens
        self._setup_special_tokens()

    def _setup_special_tokens(self):
        """Setup special tokens for thinking."""
        special_tokens = {
            "thinking_start_token": "<|thinking|>",
            "thinking_end_token": "<|/thinking|>",
            "response_start_token": "<|response|>",
            "response_end_token": "<|/response|>",
        }

        # Add special tokens if not present
        new_tokens = []
        for token_name, token in special_tokens.items():
            if token not in self.tokenizer.get_vocab():
                new_tokens.append(token)

        if new_tokens:
            self.tokenizer.add_tokens(new_tokens)

        # Store token IDs
        self.thinking_start_id = self.tokenizer.convert_tokens_to_ids("<|thinking|>")
        self.thinking_end_id = self.tokenizer.convert_tokens_to_ids("<|/thinking|>")
        self.response_start_id = self.tokenizer.convert_tokens_to_ids("<|response|>")
        self.response_end_id = self.tokenizer.convert_tokens_to_ids("<|/response|>")

    def _load_data(self) -> List[Dict[str, Any]]:
        """Load data from file."""
        if self.data_path.suffix == '.json':
            with open(self.data_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
        elif self.data_path.suffix == '.jsonl':
            data = []
            with open(self.data_path, 'r', encoding='utf-8') as f:
                for line in f:
                    data.append(json.loads(line.strip()))
        else:
            raise ValueError(f"Unsupported file format: {self.data_path.suffix}")

        # Filter by split if specified
        if isinstance(data, dict) and self.split in data:
            data = data[self.split]
        elif isinstance(data, list):
            # Assume all data is for the current split
            pass
        else:
            raise ValueError(f"Invalid data format for split {self.split}")

        return data

    def _should_include_thinking(self, sample: Dict[str, Any]) -> bool:
        """Determine if a sample should include thinking."""
        # Check if sample has explicit thinking requirement
        if "requires_thinking" in sample:
            return sample["requires_thinking"]

        # Check if sample has thinking content
        if "thinking" in sample and sample["thinking"]:
            return True

        # Random assignment based on thinking_ratio
        return random.random() < self.thinking_ratio

    def _format_sample(self, sample: Dict[str, Any]) -> Dict[str, str]:
        """Format a sample for training."""
        # Extract components
        instruction = sample.get("instruction", "")
        input_text = sample.get("input", "")
        thinking = sample.get("thinking", "")
        response = sample.get("output", sample.get("response", ""))

        # Combine instruction and input
        if input_text:
            prompt = f"{instruction}\n\nInput: {input_text}\n\n"
        else:
            prompt = f"{instruction}\n\n"

        # Determine if thinking should be included
        include_thinking = self._should_include_thinking(sample)

        if include_thinking and thinking and self.include_thoughts:
            # Format with thinking
            full_text = (
                f"{prompt}"
                f"<|thinking|>\n{thinking}\n<|/thinking|>\n"
                f"<|response|>\n{response}\n<|/response|>"
            )
            thinking_text = f"<|thinking|>\n{thinking}\n<|/thinking|>"
        else:
            # Format without thinking
            full_text = f"{prompt}<|response|>\n{response}\n<|/response|>"
            thinking_text = ""

        return {
            "prompt": prompt,
            "full_text": full_text,
            "thinking_text": thinking_text,
            "response": response,
            "requires_thinking": include_thinking,
        }

    def __len__(self) -> int:
        return len(self.samples)

    def __getitem__(self, idx: int) -> Dict[str, torch.Tensor]:
        """Get a single sample."""
        sample = self.samples[idx]
        formatted = self._format_sample(sample)

        # Tokenize full text
        full_encoding = self.tokenizer(
            formatted["full_text"],
            max_length=self.max_length,
            padding="max_length",
            truncation=True,
            return_tensors="pt",
        )

        # Tokenize prompt only
        prompt_encoding = self.tokenizer(
            formatted["prompt"],
            max_length=self.max_length,
            padding=False,
            truncation=True,
            return_tensors="pt",
        )

        # Create labels (mask prompt tokens)
        labels = full_encoding["input_ids"].clone()
        prompt_length = prompt_encoding["input_ids"].size(1)
        labels[0, :prompt_length] = -100  # Ignore prompt tokens in loss

        # Tokenize thinking text if present
        thinking_ids = None
        thinking_labels = None
        if formatted["thinking_text"]:
            thinking_encoding = self.tokenizer(
                formatted["thinking_text"],
                max_length=self.max_length // 2,  # Limit thinking length
                padding="max_length",
                truncation=True,
                return_tensors="pt",
            )
            thinking_ids = thinking_encoding["input_ids"]
            thinking_labels = thinking_ids.clone()
            # Don't mask any tokens for thinking loss

        result = {
            "input_ids": full_encoding["input_ids"].squeeze(0),
            "attention_mask": full_encoding["attention_mask"].squeeze(0),
            "labels": labels.squeeze(0),
            "requires_thinking": torch.tensor(formatted["requires_thinking"], dtype=torch.float),
        }

        if thinking_ids is not None:
            result["thinking_ids"] = thinking_ids.squeeze(0)
            result["thinking_attention_mask"] = thinking_encoding["attention_mask"].squeeze(0)
            result["thinking_labels"] = thinking_labels.squeeze(0)

        return result


class ThinkerDataLoader:
    """
    Data loader wrapper for ThinkerLLM training.
    """

    def __init__(
        self,
        dataset: ThinkerDataset,
        batch_size: int = 8,
        shuffle: bool = True,
        num_workers: int = 0,
        pin_memory: bool = True,
    ):
        self.dataset = dataset
        self.batch_size = batch_size
        self.shuffle = shuffle
        self.num_workers = num_workers
        self.pin_memory = pin_memory

    def _collate_fn(self, batch: List[Dict[str, torch.Tensor]]) -> Dict[str, torch.Tensor]:
        """Custom collate function for batching."""
        # Standard keys that should always be present
        standard_keys = ["input_ids", "attention_mask", "labels", "requires_thinking"]

        # Optional keys for thinking
        thinking_keys = ["thinking_ids", "thinking_attention_mask", "thinking_labels"]

        # Collate standard keys
        collated = {}
        for key in standard_keys:
            if key in batch[0]:
                collated[key] = torch.stack([item[key] for item in batch])

        # Collate thinking keys (only if present in all samples)
        for key in thinking_keys:
            if all(key in item for item in batch):
                collated[key] = torch.stack([item[key] for item in batch])

        return collated

    def get_dataloader(self) -> DataLoader:
        """Get PyTorch DataLoader."""
        return DataLoader(
            self.dataset,
            batch_size=self.batch_size,
            shuffle=self.shuffle,
            num_workers=self.num_workers,
            pin_memory=self.pin_memory,
            collate_fn=self._collate_fn,
        )


def create_synthetic_data(
    num_samples: int = 1000,
    output_path: str = "synthetic_data.jsonl",
    thinking_ratio: float = 0.3,
) -> None:
    """
    Create synthetic training data for ThinkerLLM.

    Args:
        num_samples: Number of samples to generate
        output_path: Path to save the data
        thinking_ratio: Ratio of samples that require thinking
    """

    # Sample instructions that require different levels of thinking
    simple_instructions = [
        "What is the capital of France?",
        "Translate 'hello' to Spanish.",
        "What is 2 + 2?",
        "Name a color.",
        "What day comes after Monday?",
    ]

    thinking_instructions = [
        "Explain the process of photosynthesis and its importance to life on Earth.",
        "Compare and contrast democracy and authoritarianism.",
        "Solve this math problem step by step: If a train travels 120 miles in 2 hours, what is its average speed?",
        "Analyze the causes and effects of climate change.",
        "Describe how to plan a successful project from start to finish.",
    ]

    samples = []

    for i in range(num_samples):
        requires_thinking = random.random() < thinking_ratio

        if requires_thinking:
            instruction = random.choice(thinking_instructions)
            # Generate thinking process
            thinking = f"Let me think about this step by step.\n\nFirst, I need to consider the key components of this question.\n\nThen, I should analyze each part carefully.\n\nFinally, I'll synthesize my understanding into a comprehensive response."
        else:
            instruction = random.choice(simple_instructions)
            thinking = ""

        # Generate response (simplified)
        if "capital of France" in instruction:
            response = "The capital of France is Paris."
        elif "translate" in instruction.lower():
            response = "The Spanish translation of 'hello' is 'hola'."
        elif "2 + 2" in instruction:
            response = "2 + 2 equals 4."
        elif "color" in instruction:
            response = "Blue is a color."
        elif "Monday" in instruction:
            response = "Tuesday comes after Monday."
        else:
            response = f"This is a response to: {instruction}"

        sample = {
            "instruction": instruction,
            "thinking": thinking,
            "output": response,
            "requires_thinking": requires_thinking,
        }

        samples.append(sample)

    # Save to file
    with open(output_path, 'w', encoding='utf-8') as f:
        for sample in samples:
            f.write(json.dumps(sample) + '\n')

    print(f"Generated {num_samples} synthetic samples and saved to {output_path}")


def prepare_dataset(
    data_path: str,
    tokenizer_name: str = "gpt2",
    max_length: int = 2048,
    thinking_ratio: float = 0.3,
    batch_size: int = 8,
    split: str = "train",
) -> Tuple[ThinkerDataset, ThinkerDataLoader]:
    """
    Prepare dataset and dataloader for training.

    Args:
        data_path: Path to training data
        tokenizer_name: Name or path of tokenizer
        max_length: Maximum sequence length
        thinking_ratio: Ratio of samples requiring thinking
        batch_size: Batch size for training
        split: Data split to use

    Returns:
        Tuple of (dataset, dataloader)
    """
    # Load tokenizer
    tokenizer = AutoTokenizer.from_pretrained(tokenizer_name)

    # Add pad token if not present
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token

    # Create dataset
    dataset = ThinkerDataset(
        data_path=data_path,
        tokenizer=tokenizer,
        max_length=max_length,
        thinking_ratio=thinking_ratio,
        split=split,
    )

    # Create dataloader
    dataloader = ThinkerDataLoader(
        dataset=dataset,
        batch_size=batch_size,
        shuffle=(split == "train"),
    )

    return dataset, dataloader
