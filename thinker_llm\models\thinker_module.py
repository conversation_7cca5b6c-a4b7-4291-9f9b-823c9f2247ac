"""
ThinkerModule: Non-autoregressive reasoning component.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Optional, Tuple, Dict, Any
import math

from .attention import ThinkerAttention
from ..utils.config import ModelConfig


class ThinkerBlock(nn.Module):
    """
    Single transformer block for ThinkerModule with non-autoregressive attention.
    """
    
    def __init__(
        self,
        hidden_size: int,
        num_heads: int,
        intermediate_size: int,
        dropout: float = 0.1,
        use_flash_attention: bool = True,
    ):
        super().__init__()
        self.hidden_size = hidden_size
        self.num_heads = num_heads
        self.intermediate_size = intermediate_size
        
        # Self-attention layer
        self.self_attention = ThinkerAttention(
            hidden_size=hidden_size,
            num_heads=num_heads,
            dropout=dropout,
            use_flash_attention=use_flash_attention,
        )
        
        # Feed-forward network
        self.ffn = nn.Sequential(
            nn.Linear(hidden_size, intermediate_size),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(intermediate_size, hidden_size),
            nn.Dropout(dropout),
        )
        
        # Layer normalization
        self.ln1 = nn.LayerNorm(hidden_size)
        self.ln2 = nn.LayerNorm(hidden_size)
        
    def forward(
        self,
        hidden_states: torch.Tensor,
        attention_mask: Optional[torch.Tensor] = None,
        position_embeddings: Optional[torch.Tensor] = None,
    ) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Forward pass through ThinkerBlock.
        
        Args:
            hidden_states: Input tensor (batch_size, seq_len, hidden_size)
            attention_mask: Optional attention mask
            position_embeddings: Optional position embeddings
            
        Returns:
            Tuple of (output_states, attention_weights)
        """
        # Self-attention with residual connection
        attn_output, attn_weights = self.self_attention(
            hidden_states=hidden_states,
            attention_mask=attention_mask,
            position_embeddings=position_embeddings,
        )
        hidden_states = self.ln1(hidden_states + attn_output)
        
        # Feed-forward with residual connection
        ffn_output = self.ffn(hidden_states)
        hidden_states = self.ln2(hidden_states + ffn_output)
        
        return hidden_states, attn_weights


class ThinkerModule(nn.Module):
    """
    Non-autoregressive reasoning module that processes entire sequences
    simultaneously to perform reasoning and generate thought processes.
    """
    
    def __init__(self, config: ModelConfig):
        super().__init__()
        self.config = config
        self.hidden_size = config.thinker_hidden_size
        self.num_layers = config.thinker_num_layers
        self.num_heads = config.thinker_num_heads
        self.max_length = config.thinker_max_length
        self.vocab_size = config.vocab_size
        
        # Input embeddings
        self.token_embeddings = nn.Embedding(config.vocab_size, self.hidden_size)
        self.position_embeddings = nn.Embedding(config.thinker_max_length, self.hidden_size)
        
        # Transformer blocks
        self.blocks = nn.ModuleList([
            ThinkerBlock(
                hidden_size=self.hidden_size,
                num_heads=self.num_heads,
                intermediate_size=config.thinker_intermediate_size,
                dropout=config.thinker_dropout,
                use_flash_attention=config.use_flash_attention,
            )
            for _ in range(self.num_layers)
        ])
        
        # Output layers
        self.ln_f = nn.LayerNorm(self.hidden_size)
        
        # Thought generation head (for visible reasoning text)
        self.thought_head = nn.Linear(self.hidden_size, config.vocab_size, bias=False)
        
        # Reasoning state head (for hidden states to be projected to MainLLM)
        self.reasoning_head = nn.Sequential(
            nn.Linear(self.hidden_size, self.hidden_size),
            nn.GELU(),
            nn.Dropout(config.thinker_dropout),
            nn.Linear(self.hidden_size, self.hidden_size),
        )
        
        # Initialize weights
        self.apply(self._init_weights)
        
        # Tie embeddings if specified
        if config.tie_word_embeddings:
            self.thought_head.weight = self.token_embeddings.weight
    
    def _init_weights(self, module):
        """Initialize weights using standard transformer initialization."""
        if isinstance(module, nn.Linear):
            torch.nn.init.normal_(module.weight, mean=0.0, std=0.02)
            if module.bias is not None:
                torch.nn.init.zeros_(module.bias)
        elif isinstance(module, nn.Embedding):
            torch.nn.init.normal_(module.weight, mean=0.0, std=0.02)
        elif isinstance(module, nn.LayerNorm):
            torch.nn.init.zeros_(module.bias)
            torch.nn.init.ones_(module.weight)
    
    def forward(
        self,
        input_ids: torch.Tensor,
        attention_mask: Optional[torch.Tensor] = None,
        return_dict: bool = True,
    ) -> Dict[str, torch.Tensor]:
        """
        Forward pass through ThinkerModule.
        
        Args:
            input_ids: Input token IDs (batch_size, seq_len)
            attention_mask: Optional attention mask (batch_size, seq_len)
            return_dict: Whether to return a dictionary
            
        Returns:
            Dictionary containing:
                - thought_logits: Logits for thought generation
                - reasoning_states: Hidden states for MainLLM integration
                - hidden_states: Final hidden states
                - attention_weights: Attention weights from all layers
        """
        batch_size, seq_len = input_ids.shape
        device = input_ids.device
        
        # Create position IDs
        position_ids = torch.arange(seq_len, device=device).unsqueeze(0).expand(batch_size, -1)
        
        # Embeddings
        token_embeds = self.token_embeddings(input_ids)
        position_embeds = self.position_embeddings(position_ids)
        hidden_states = token_embeds + position_embeds
        
        # Apply dropout
        hidden_states = F.dropout(hidden_states, p=self.config.thinker_dropout, training=self.training)
        
        # Pass through transformer blocks
        all_attention_weights = []
        for block in self.blocks:
            hidden_states, attn_weights = block(
                hidden_states=hidden_states,
                attention_mask=attention_mask,
                position_embeddings=None,  # Already added
            )
            all_attention_weights.append(attn_weights)
        
        # Final layer norm
        hidden_states = self.ln_f(hidden_states)
        
        # Generate outputs
        thought_logits = self.thought_head(hidden_states)
        reasoning_states = self.reasoning_head(hidden_states)
        
        if return_dict:
            return {
                "thought_logits": thought_logits,
                "reasoning_states": reasoning_states,
                "hidden_states": hidden_states,
                "attention_weights": all_attention_weights,
            }
        else:
            return thought_logits, reasoning_states, hidden_states, all_attention_weights
    
    def generate_thoughts(
        self,
        input_ids: torch.Tensor,
        attention_mask: Optional[torch.Tensor] = None,
        max_new_tokens: int = 128,
        temperature: float = 0.8,
        top_p: float = 0.9,
        do_sample: bool = True,
    ) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Generate thought sequences using the ThinkerModule.
        
        Args:
            input_ids: Input token IDs
            attention_mask: Optional attention mask
            max_new_tokens: Maximum number of new tokens to generate
            temperature: Sampling temperature
            top_p: Top-p sampling parameter
            do_sample: Whether to use sampling
            
        Returns:
            Tuple of (thought_ids, reasoning_states)
        """
        self.eval()
        
        with torch.no_grad():
            # Get initial reasoning states
            outputs = self.forward(input_ids, attention_mask)
            reasoning_states = outputs["reasoning_states"]
            
            # Generate thought sequence (non-autoregressive approach)
            # We use the thought logits directly since ThinkerModule processes
            # the entire sequence simultaneously
            thought_logits = outputs["thought_logits"]
            
            if do_sample:
                # Apply temperature
                thought_logits = thought_logits / temperature
                
                # Apply top-p filtering
                if top_p < 1.0:
                    sorted_logits, sorted_indices = torch.sort(thought_logits, descending=True, dim=-1)
                    cumulative_probs = torch.cumsum(F.softmax(sorted_logits, dim=-1), dim=-1)
                    
                    # Remove tokens with cumulative probability above the threshold
                    sorted_indices_to_remove = cumulative_probs > top_p
                    sorted_indices_to_remove[..., 1:] = sorted_indices_to_remove[..., :-1].clone()
                    sorted_indices_to_remove[..., 0] = 0
                    
                    # Scatter sorted tensors to original indexing
                    indices_to_remove = sorted_indices_to_remove.scatter(
                        dim=-1, index=sorted_indices, src=sorted_indices_to_remove
                    )
                    thought_logits[indices_to_remove] = float('-inf')
                
                # Sample from the distribution
                probs = F.softmax(thought_logits, dim=-1)
                thought_ids = torch.multinomial(probs.view(-1, probs.size(-1)), 1).view(probs.shape[:-1])
            else:
                # Greedy decoding
                thought_ids = torch.argmax(thought_logits, dim=-1)
        
        return thought_ids, reasoning_states
    
    def get_reasoning_summary(
        self,
        reasoning_states: torch.Tensor,
        attention_mask: Optional[torch.Tensor] = None,
    ) -> torch.Tensor:
        """
        Generate a summary of reasoning states for efficient integration.
        
        Args:
            reasoning_states: Reasoning states from forward pass
            attention_mask: Optional attention mask
            
        Returns:
            Summarized reasoning states
        """
        if attention_mask is not None:
            # Mask out padding tokens
            mask = attention_mask.unsqueeze(-1).expand_as(reasoning_states)
            reasoning_states = reasoning_states * mask
            
            # Compute weighted average
            lengths = attention_mask.sum(dim=1, keepdim=True).float()
            summary = reasoning_states.sum(dim=1) / lengths
        else:
            # Simple average pooling
            summary = reasoning_states.mean(dim=1)
        
        return summary
    
    def compute_complexity_score(
        self,
        input_ids: torch.Tensor,
        attention_mask: Optional[torch.Tensor] = None,
    ) -> torch.Tensor:
        """
        Compute a complexity score for the input to help the decision mechanism.
        
        Args:
            input_ids: Input token IDs
            attention_mask: Optional attention mask
            
        Returns:
            Complexity scores for each sample in the batch
        """
        with torch.no_grad():
            outputs = self.forward(input_ids, attention_mask)
            hidden_states = outputs["hidden_states"]
            
            # Compute complexity based on hidden state variance
            if attention_mask is not None:
                mask = attention_mask.unsqueeze(-1).expand_as(hidden_states)
                masked_states = hidden_states * mask
                lengths = attention_mask.sum(dim=1, keepdim=True).float()
                mean_states = masked_states.sum(dim=1) / lengths
            else:
                mean_states = hidden_states.mean(dim=1)
            
            # Compute variance as complexity measure
            if attention_mask is not None:
                diff = (hidden_states - mean_states.unsqueeze(1)) * mask
                variance = (diff ** 2).sum(dim=(1, 2)) / (lengths.squeeze() * self.hidden_size)
            else:
                diff = hidden_states - mean_states.unsqueeze(1)
                variance = (diff ** 2).mean(dim=(1, 2))
            
            # Normalize to [0, 1] range
            complexity_score = torch.sigmoid(variance)
            
        return complexity_score
